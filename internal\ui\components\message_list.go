/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"arien-ai-cli/internal/ui/styles"
	"arien-ai-cli/internal/llm"
)

// MessageList represents a scrollable message list component
type MessageList struct {
	BaseComponent
	messages     []ChatMessage
	scrollOffset int
	maxMessages  int
	showSystem   bool
}

// NewMessageList creates a new message list component
func NewMessageList() *MessageList {
	return &MessageList{
		BaseComponent: BaseComponent{},
		messages:      []ChatMessage{},
		scrollOffset:  0,
		maxMessages:   1000, // Limit to prevent memory issues
		showSystem:    false, // Don't show system messages by default
	}
}

// Update handles messages and updates the message list
func (ml *MessageList) Update(msg tea.Msg) (Component, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		ml.SetSize(msg.Width, msg.Height)
		return ml, nil
		
	case tea.KeyMsg:
		switch msg.String() {
		case "up", "k":
			if ml.scrollOffset > 0 {
				ml.scrollOffset--
			}
			return ml, nil
			
		case "down", "j":
			maxScroll := ml.getMaxScroll()
			if ml.scrollOffset < maxScroll {
				ml.scrollOffset++
			}
			return ml, nil
			
		case "home":
			ml.scrollOffset = 0
			return ml, nil
			
		case "end":
			ml.ScrollToBottom()
			return ml, nil
		}
		
	case ComponentMessage:
		switch msg.Type {
		case MsgTypeMessage:
			if message, ok := msg.Payload.(ChatMessage); ok {
				ml.AddMessage(message)
			}
			return ml, nil
		}
	}
	
	return ml, nil
}

// View renders the message list
func (ml *MessageList) View() string {
	if ml.width == 0 || ml.height == 0 {
		return ""
	}
	
	lines := ml.renderMessages()
	
	// Calculate visible lines based on scroll offset
	startLine := ml.scrollOffset
	endLine := startLine + ml.height
	
	if startLine >= len(lines) {
		startLine = len(lines) - ml.height
		if startLine < 0 {
			startLine = 0
		}
	}
	
	if endLine > len(lines) {
		endLine = len(lines)
	}
	
	var visibleLines []string
	if startLine < endLine {
		visibleLines = lines[startLine:endLine]
	}
	
	// Pad with empty lines if needed
	for len(visibleLines) < ml.height {
		visibleLines = append(visibleLines, "")
	}
	
	return strings.Join(visibleLines, "\n")
}

// AddMessage adds a new message to the list
func (ml *MessageList) AddMessage(message ChatMessage) {
	ml.messages = append(ml.messages, message)
	
	// Limit message count to prevent memory issues
	if len(ml.messages) > ml.maxMessages {
		ml.messages = ml.messages[len(ml.messages)-ml.maxMessages:]
	}
	
	// Auto-scroll to bottom when new message is added
	ml.ScrollToBottom()
}

// SetMessages sets the entire message list
func (ml *MessageList) SetMessages(messages []ChatMessage) {
	ml.messages = make([]ChatMessage, len(messages))
	copy(ml.messages, messages)
	ml.ScrollToBottom()
}

// ScrollToBottom scrolls to the bottom of the message list
func (ml *MessageList) ScrollToBottom() {
	ml.scrollOffset = ml.getMaxScroll()
}

// GetMessageCount returns the number of messages
func (ml *MessageList) GetMessageCount() int {
	return len(ml.messages)
}

// Clear removes all messages
func (ml *MessageList) Clear() {
	ml.messages = []ChatMessage{}
	ml.scrollOffset = 0
}

// SetShowSystem enables or disables system message display
func (ml *MessageList) SetShowSystem(show bool) {
	ml.showSystem = show
}

// renderMessages renders all messages into lines
func (ml *MessageList) renderMessages() []string {
	var lines []string
	
	for _, msg := range ml.messages {
		if msg.Role == "system" && !ml.showSystem {
			continue // Skip system messages if not enabled
		}
		
		messageLines := ml.renderMessage(msg)
		lines = append(lines, messageLines...)
		lines = append(lines, "") // Add spacing between messages
	}
	
	return lines
}

// renderMessage renders a single message
func (ml *MessageList) renderMessage(msg ChatMessage) []string {
	timestamp := msg.Timestamp.Format("15:04")
	
	var lines []string
	var style func() lipgloss.Style
	var icon string
	
	switch msg.Role {
	case "user":
		style = styles.UserMessageStyle
		icon = "👤"
	case "assistant":
		style = styles.AssistantMessageStyle
		icon = "🤖"
	case "system":
		style = styles.SystemMessageStyle
		icon = "⚙️"
	default:
		style = styles.MessageStyle
		icon = "💬"
	}
	
	// Main message content
	content := fmt.Sprintf("%s [%s] %s", icon, timestamp, msg.Content)
	
	// Wrap content to fit width
	wrappedContent := ml.wrapText(content, ml.width-2) // Leave margin for padding
	
	for _, line := range wrappedContent {
		styledLine := style().Render(line)
		lines = append(lines, styledLine)
	}
	
	// Render tool calls if present
	if len(msg.ToolCalls) > 0 {
		toolLines := ml.renderToolCalls(msg.ToolCalls)
		lines = append(lines, toolLines...)
	}
	
	return lines
}

// renderToolCalls renders function calls
func (ml *MessageList) renderToolCalls(toolCalls []llm.ToolCall) []string {
	var lines []string
	
	for _, call := range toolCalls {
		toolLine := fmt.Sprintf("🔧 Called: %s(%s)", call.Function.Name, call.Function.Arguments)
		styledLine := styles.ToolCallStyle().Render(toolLine)
		
		// Wrap tool call content
		wrappedLines := ml.wrapText(styledLine, ml.width-2)
		lines = append(lines, wrappedLines...)
	}
	
	return lines
}

// wrapText wraps text to fit within the specified width
func (ml *MessageList) wrapText(text string, width int) []string {
	if width <= 0 {
		return []string{text}
	}
	
	words := strings.Fields(text)
	if len(words) == 0 {
		return []string{""}
	}
	
	var lines []string
	var currentLine strings.Builder
	
	for _, word := range words {
		// Check if adding this word would exceed the width
		if currentLine.Len() > 0 && currentLine.Len()+len(word)+1 > width {
			lines = append(lines, currentLine.String())
			currentLine.Reset()
		}
		
		if currentLine.Len() > 0 {
			currentLine.WriteString(" ")
		}
		currentLine.WriteString(word)
	}
	
	if currentLine.Len() > 0 {
		lines = append(lines, currentLine.String())
	}
	
	return lines
}

// getMaxScroll calculates the maximum scroll offset
func (ml *MessageList) getMaxScroll() int {
	lines := ml.renderMessages()
	maxScroll := len(lines) - ml.height
	if maxScroll < 0 {
		maxScroll = 0
	}
	return maxScroll
}

// GetScrollPosition returns current scroll position info
func (ml *MessageList) GetScrollPosition() (current, max int) {
	return ml.scrollOffset, ml.getMaxScroll()
}

// SetMaxMessages sets the maximum number of messages to keep
func (ml *MessageList) SetMaxMessages(max int) {
	ml.maxMessages = max
	
	// Trim messages if needed
	if len(ml.messages) > max {
		ml.messages = ml.messages[len(ml.messages)-max:]
	}
}
