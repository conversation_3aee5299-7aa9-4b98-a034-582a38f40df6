/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package styles

import (
	"github.com/charmbracelet/lipgloss"
)

// Theme represents a UI theme with color definitions
type Theme struct {
	Name        string
	Description string
	Primary     lipgloss.Color
	Secondary   lipgloss.Color
	Accent      lipgloss.Color
	Background  lipgloss.Color
	Foreground  lipgloss.Color
	Success     lipgloss.Color
	Warning     lipgloss.Color
	Error       lipgloss.Color
	Muted       lipgloss.Color
	Border      lipgloss.Color
}

// Available themes
var themes = map[string]Theme{
	"default": {
		Name:        "Default",
		Description: "Clean and modern default theme",
		Primary:     lipgloss.Color("#007ACC"),
		Secondary:   lipgloss.Color("#6C7B7F"),
		Accent:      lipgloss.Color("#FF6B35"),
		Background:  lipgloss.Color("#FFFFFF"),
		Foreground:  lipgloss.Color("#2D3748"),
		Success:     lipgloss.Color("#38A169"),
		Warning:     lipgloss.Color("#D69E2E"),
		Error:       lipgloss.Color("#E53E3E"),
		Muted:       lipgloss.Color("#A0AEC0"),
		Border:      lipgloss.Color("#E2E8F0"),
	},
	"dark": {
		Name:        "Dark",
		Description: "Dark theme for low-light environments",
		Primary:     lipgloss.Color("#61DAFB"),
		Secondary:   lipgloss.Color("#98A2B3"),
		Accent:      lipgloss.Color("#F56565"),
		Background:  lipgloss.Color("#1A202C"),
		Foreground:  lipgloss.Color("#F7FAFC"),
		Success:     lipgloss.Color("#68D391"),
		Warning:     lipgloss.Color("#F6E05E"),
		Error:       lipgloss.Color("#FC8181"),
		Muted:       lipgloss.Color("#718096"),
		Border:      lipgloss.Color("#4A5568"),
	},
	"cyberpunk": {
		Name:        "Cyberpunk",
		Description: "Neon-inspired cyberpunk theme",
		Primary:     lipgloss.Color("#00FF41"),
		Secondary:   lipgloss.Color("#FF0080"),
		Accent:      lipgloss.Color("#FFFF00"),
		Background:  lipgloss.Color("#0D0208"),
		Foreground:  lipgloss.Color("#00FF41"),
		Success:     lipgloss.Color("#39FF14"),
		Warning:     lipgloss.Color("#FFFF00"),
		Error:       lipgloss.Color("#FF073A"),
		Muted:       lipgloss.Color("#8B5CF6"),
		Border:      lipgloss.Color("#FF0080"),
	},
	"ocean": {
		Name:        "Ocean",
		Description: "Calm ocean-inspired blue theme",
		Primary:     lipgloss.Color("#0EA5E9"),
		Secondary:   lipgloss.Color("#0284C7"),
		Accent:      lipgloss.Color("#06B6D4"),
		Background:  lipgloss.Color("#F0F9FF"),
		Foreground:  lipgloss.Color("#0C4A6E"),
		Success:     lipgloss.Color("#059669"),
		Warning:     lipgloss.Color("#D97706"),
		Error:       lipgloss.Color("#DC2626"),
		Muted:       lipgloss.Color("#64748B"),
		Border:      lipgloss.Color("#BAE6FD"),
	},
	"forest": {
		Name:        "Forest",
		Description: "Nature-inspired green theme",
		Primary:     lipgloss.Color("#16A34A"),
		Secondary:   lipgloss.Color("#15803D"),
		Accent:      lipgloss.Color("#84CC16"),
		Background:  lipgloss.Color("#F0FDF4"),
		Foreground:  lipgloss.Color("#14532D"),
		Success:     lipgloss.Color("#22C55E"),
		Warning:     lipgloss.Color("#EAB308"),
		Error:       lipgloss.Color("#EF4444"),
		Muted:       lipgloss.Color("#6B7280"),
		Border:      lipgloss.Color("#BBF7D0"),
	},
	"sunset": {
		Name:        "Sunset",
		Description: "Warm sunset-inspired theme",
		Primary:     lipgloss.Color("#FF6347"),
		Secondary:   lipgloss.Color("#FF7F50"),
		Accent:      lipgloss.Color("#FFD700"),
		Background:  lipgloss.Color("#FFF8DC"),
		Foreground:  lipgloss.Color("#8B4513"),
		Success:     lipgloss.Color("#32CD32"),
		Warning:     lipgloss.Color("#FF8C00"),
		Error:       lipgloss.Color("#DC143C"),
		Muted:       lipgloss.Color("#D2B48C"),
		Border:      lipgloss.Color("#DEB887"),
	},
}

var currentTheme Theme

// GetTheme returns the current theme
func GetTheme() Theme {
	return currentTheme
}

// SetTheme sets the current theme by name
func SetTheme(name string) bool {
	if theme, exists := themes[name]; exists {
		currentTheme = theme
		return true
	}
	return false
}

// GetAvailableThemes returns all available themes
func GetAvailableThemes() map[string]Theme {
	return themes
}

// GetThemeNames returns all theme names
func GetThemeNames() []string {
	var names []string
	for name := range themes {
		names = append(names, name)
	}
	return names
}

// Initialize sets the default theme
func Initialize() {
	currentTheme = themes["default"]
}

// IsValidTheme checks if a theme name is valid
func IsValidTheme(name string) bool {
	_, exists := themes[name]
	return exists
}
