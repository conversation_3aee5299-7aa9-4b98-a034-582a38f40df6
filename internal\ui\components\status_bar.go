/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"arien-ai-cli/internal/ui/styles"
)

// StatusBar represents a status bar component
type StatusBar struct {
	BaseComponent
	status      string
	error       error
	loading     bool
	loadingText string
	showTime    bool
	persistent  bool
	timeout     time.Duration
	lastUpdate  time.Time
}

// NewStatusBar creates a new status bar component
func NewStatusBar() *StatusBar {
	return &StatusBar{
		BaseComponent: BaseComponent{},
		status:        "",
		error:         nil,
		loading:       false,
		loadingText:   "Loading...",
		showTime:      true,
		persistent:    false,
		timeout:       5 * time.Second,
		lastUpdate:    time.Now(),
	}
}

// Update handles messages and updates the status bar
func (sb *StatusBar) Update(msg tea.Msg) (Component, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		sb.SetSize(msg.Width, 1) // Status bar is always 1 line
		return sb, nil
		
	case ComponentMessage:
		switch msg.Type {
		case MsgTypeStatus:
			if status, ok := msg.Payload.(string); ok {
				sb.SetStatus(status)
			}
			return sb, nil
			
		case MsgTypeError:
			if errorMsg, ok := msg.Payload.(ErrorMsg); ok {
				sb.SetError(errorMsg.Error)
			}
			return sb, nil
		}
		
	case time.Time:
		// Auto-clear non-persistent status after timeout
		if !sb.persistent && !sb.loading && sb.error == nil {
			if time.Since(sb.lastUpdate) > sb.timeout {
				sb.status = ""
			}
		}
		return sb, sb.tickCmd()
	}
	
	return sb, nil
}

// View renders the status bar
func (sb *StatusBar) View() string {
	if sb.width == 0 {
		return ""
	}
	
	var content string
	var style func() lipgloss.Style
	
	// Priority: Error > Loading > Status
	if sb.error != nil {
		content = fmt.Sprintf("❌ Error: %v", sb.error)
		style = styles.ErrorStyle
	} else if sb.loading {
		content = fmt.Sprintf("⏳ %s", sb.loadingText)
		style = styles.LoadingStyle
	} else if sb.status != "" {
		content = sb.status
		style = styles.StatusStyle
	} else {
		// Show current time if no other content
		if sb.showTime {
			content = time.Now().Format("15:04:05")
			style = styles.MutedStyle
		} else {
			content = ""
			style = styles.StatusStyle
		}
	}
	
	if content == "" {
		return strings.Repeat(" ", sb.width)
	}
	
	// Apply styling and fit to width
	styledContent := style().
		Width(sb.width).
		Align(lipgloss.Center).
		Render(content)
	
	return styledContent
}

// SetStatus sets the status message
func (sb *StatusBar) SetStatus(status string) {
	sb.status = status
	sb.error = nil
	sb.lastUpdate = time.Now()
}

// SetError sets an error message
func (sb *StatusBar) SetError(err error) {
	sb.error = err
	sb.status = ""
	sb.loading = false
	sb.lastUpdate = time.Now()
}

// ClearError clears the current error
func (sb *StatusBar) ClearError() {
	sb.error = nil
	sb.lastUpdate = time.Now()
}

// SetLoading sets the loading state
func (sb *StatusBar) SetLoading(loading bool) {
	sb.loading = loading
	if loading {
		sb.error = nil
	}
	sb.lastUpdate = time.Now()
}

// IsLoading returns whether the status bar is in loading state
func (sb *StatusBar) IsLoading() bool {
	return sb.loading
}

// SetLoadingText sets the loading message
func (sb *StatusBar) SetLoadingText(text string) {
	sb.loadingText = text
}

// GetLoadingText returns the current loading text
func (sb *StatusBar) GetLoadingText() string {
	return sb.loadingText
}

// SetShowTime enables or disables time display
func (sb *StatusBar) SetShowTime(show bool) {
	sb.showTime = show
}

// IsShowTime returns whether time display is enabled
func (sb *StatusBar) IsShowTime() bool {
	return sb.showTime
}

// SetPersistent sets whether the status should persist or auto-clear
func (sb *StatusBar) SetPersistent(persistent bool) {
	sb.persistent = persistent
}

// IsPersistent returns whether the status is persistent
func (sb *StatusBar) IsPersistent() bool {
	return sb.persistent
}

// SetTimeout sets the auto-clear timeout for non-persistent status
func (sb *StatusBar) SetTimeout(timeout time.Duration) {
	sb.timeout = timeout
}

// GetTimeout returns the current timeout
func (sb *StatusBar) GetTimeout() time.Duration {
	return sb.timeout
}

// Clear clears all status, error, and loading states
func (sb *StatusBar) Clear() {
	sb.status = ""
	sb.error = nil
	sb.loading = false
	sb.lastUpdate = time.Now()
}

// GetStatus returns the current status
func (sb *StatusBar) GetStatus() string {
	return sb.status
}

// GetError returns the current error
func (sb *StatusBar) GetError() error {
	return sb.error
}

// HasContent returns whether the status bar has any content to display
func (sb *StatusBar) HasContent() bool {
	return sb.error != nil || sb.loading || sb.status != "" || sb.showTime
}

// tickCmd returns a command for periodic updates
func (sb *StatusBar) tickCmd() tea.Cmd {
	return tea.Tick(time.Second, func(t time.Time) tea.Msg {
		return t
	})
}

// GetHeight returns the status bar height (always 1)
func (sb *StatusBar) GetHeight() int {
	return 1
}

// SetLoadingWithText sets loading state with custom text
func (sb *StatusBar) SetLoadingWithText(text string) {
	sb.loadingText = text
	sb.SetLoading(true)
}

// ShowTemporaryStatus shows a status message that will auto-clear
func (sb *StatusBar) ShowTemporaryStatus(status string, duration time.Duration) {
	sb.SetStatus(status)
	sb.persistent = false
	sb.timeout = duration
}

// ShowPersistentStatus shows a status message that won't auto-clear
func (sb *StatusBar) ShowPersistentStatus(status string) {
	sb.SetStatus(status)
	sb.persistent = true
}

// ShowSuccess shows a success message
func (sb *StatusBar) ShowSuccess(message string) {
	sb.SetStatus(fmt.Sprintf("✅ %s", message))
	sb.persistent = false
	sb.timeout = 3 * time.Second
}

// ShowWarning shows a warning message
func (sb *StatusBar) ShowWarning(message string) {
	sb.SetStatus(fmt.Sprintf("⚠️ %s", message))
	sb.persistent = false
	sb.timeout = 5 * time.Second
}

// ShowInfo shows an info message
func (sb *StatusBar) ShowInfo(message string) {
	sb.SetStatus(fmt.Sprintf("ℹ️ %s", message))
	sb.persistent = false
	sb.timeout = 3 * time.Second
}
