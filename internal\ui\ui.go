/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package ui

import (
	"fmt"

	"github.com/charmbracelet/bubbletea"
	"arien-ai-cli/internal/config"
	"arien-ai-cli/internal/logger"
	"arien-ai-cli/internal/ui/models"
	"arien-ai-cli/internal/ui/styles"
)

// StartInteractiveMode starts the interactive chat mode with modular components
func StartInteractiveMode() error {
	logger.Info("Starting interactive mode with modular components")

	// Initialize themes
	styles.Initialize()

	// Set theme from config
	if config.AppConfig.DefaultTheme != "" {
		if !styles.SetTheme(config.AppConfig.DefaultTheme) {
			logger.Warning(fmt.Sprintf("Theme '%s' not found, using default", config.AppConfig.DefaultTheme))
		}
	}

	// Use defaults from config
	provider := config.AppConfig.DefaultProvider
	if provider == "" {
		provider = "openai"
	}

	model := ""
	if providerConfig, exists := config.AppConfig.Providers[provider]; exists {
		model = providerConfig.DefaultModel
	}
	if model == "" {
		model = "gpt-4"
	}

	// Create and run modular chat model
	chatModel := models.NewChatModel(provider, model)
	program := tea.NewProgram(chatModel, tea.WithAltScreen())

	_, err := program.Run()
	if err != nil {
		return fmt.Errorf("failed to run interactive mode: %w", err)
	}

	return nil
}

// StartChat starts a chat session with specific parameters using modular components
func StartChat(provider, model, theme string) error {
	logger.Info(fmt.Sprintf("Starting modular chat with provider: %s, model: %s, theme: %s", provider, model, theme))

	// Initialize themes
	styles.Initialize()

	// Set theme
	if theme != "" {
		if !styles.SetTheme(theme) {
			logger.Warning(fmt.Sprintf("Theme '%s' not found, using default", theme))
		}
	}

	// Use defaults if not specified
	if provider == "" {
		provider = config.AppConfig.DefaultProvider
		if provider == "" {
			provider = "openai"
		}
	}

	if model == "" {
		if providerConfig, exists := config.AppConfig.Providers[provider]; exists {
			model = providerConfig.DefaultModel
		}
		if model == "" {
			model = "gpt-4"
		}
	}

	// Create and run modular chat model
	chatModel := models.NewChatModel(provider, model)
	program := tea.NewProgram(chatModel, tea.WithAltScreen())

	_, err := program.Run()
	if err != nil {
		return fmt.Errorf("failed to start chat: %w", err)
	}

	return nil
}

// ShowThemeSelector shows the theme selection interface with modular components
func ShowThemeSelector() error {
	logger.Info("Starting modular theme selector")

	// Initialize themes
	styles.Initialize()

	// Create and run theme selector
	themeSelectorModel := NewThemeSelectorModel()
	program := tea.NewProgram(themeSelectorModel, tea.WithAltScreen())

	_, err := program.Run()
	if err != nil {
		return fmt.Errorf("failed to show theme selector: %w", err)
	}

	return nil
}
