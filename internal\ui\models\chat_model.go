/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package models

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbletea"
	"arien-ai-cli/internal/llm"
	"arien-ai-cli/internal/config"
	"arien-ai-cli/internal/logger"
	"arien-ai-cli/internal/tools"
	"arien-ai-cli/internal/ui/components"
	"arien-ai-cli/internal/ui/styles"
)

// ChatModel represents the main chat interface using modular components
type ChatModel struct {
	// Core managers
	llmManager  *llm.Manager
	toolManager *tools.Manager
	
	// Configuration
	provider string
	model    string
	
	// UI Components
	header      *components.Header
	messageList *components.MessageList
	inputArea   *components.InputArea
	statusBar   *components.StatusBar
	toolDisplay *components.ToolDisplay
	
	// State
	width     int
	height    int
	ready     bool
	streaming bool
	err       error
	
	// Layout
	headerHeight    int
	statusHeight    int
	inputHeight     int
	showToolDisplay bool
	toolDisplayWidth int
}

// NewChatModel creates a new modular chat model
func NewChatModel(provider, model string) *ChatModel {
	return &ChatModel{
		llmManager:       llm.NewManager(),
		toolManager:      tools.NewManager(),
		provider:         provider,
		model:            model,
		header:           components.NewHeader(),
		messageList:      components.NewMessageList(),
		inputArea:        components.NewInputArea(),
		statusBar:        components.NewStatusBar(),
		toolDisplay:      components.NewToolDisplay(),
		ready:            false,
		streaming:        false,
		headerHeight:     3,
		statusHeight:     1,
		inputHeight:      3,
		showToolDisplay:  true,
		toolDisplayWidth: 40, // 40% of width for tool display
	}
}

// Init initializes the chat model and all components
func (m *ChatModel) Init() tea.Cmd {
	// Initialize components
	m.header.SetProvider(m.provider, m.model)
	m.statusBar.SetLoadingWithText("Initializing Arien AI CLI...")
	
	return tea.Batch(
		tea.EnterAltScreen,
		func() tea.Msg {
			// Initialize managers
			if err := m.llmManager.Initialize(); err != nil {
				return components.ErrorMsg{Error: err}
			}
			if err := m.toolManager.Initialize(); err != nil {
				return components.ErrorMsg{Error: err}
			}
			return readyMsg{}
		},
	)
}

// Update handles messages and updates the model and components
func (m *ChatModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd
	
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.updateLayout()
		return m, nil
		
	case tea.KeyMsg:
		if !m.ready {
			// Only allow quit during initialization
			switch msg.String() {
			case "ctrl+c", "esc", "q":
				return m, tea.Quit
			}
			return m, nil
		}
		
		switch msg.String() {
		case "ctrl+c", "esc":
			return m, tea.Quit
			
		case "ctrl+t":
			// Toggle tool display
			m.showToolDisplay = !m.showToolDisplay
			m.updateLayout()
			return m, nil
			
		case "enter":
			if !m.streaming && m.inputArea.IsEnabled() {
				input := m.inputArea.GetInput()
				if strings.TrimSpace(input) != "" {
					m.inputArea.ClearInput()
					return m, m.sendMessage(input)
				}
			}
			return m, nil
		}
		
		// Forward key messages to input area
		if m.inputArea.IsEnabled() {
			var cmd tea.Cmd
			component, cmd := m.inputArea.Update(msg)
			m.inputArea = component.(*components.InputArea)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
		}
		
	case readyMsg:
		m.ready = true
		m.statusBar.Clear()
		m.header.SetConnectionState(true)
		
		// Add system message if configured
		systemPrompt := config.AppConfig.SystemPrompt
		if systemPrompt != "" {
			systemMessage := components.ChatMessage{
				Role:      "system",
				Content:   systemPrompt,
				Timestamp: time.Now(),
			}
			m.messageList.AddMessage(systemMessage)
		}
		
		m.statusBar.ShowInfo("Ready! Type your message and press Enter.")
		return m, nil
		
	case responseMsg:
		m.streaming = false
		m.inputArea.SetEnabled(true)
		
		// Add assistant message
		assistantMessage := components.ChatMessage{
			Role:      "assistant",
			Content:   msg.content,
			Timestamp: time.Now(),
			ToolCalls: msg.toolCalls,
		}
		m.messageList.AddMessage(assistantMessage)
		
		// Show tool calls in tool display
		for _, toolCall := range msg.toolCalls {
			m.toolDisplay.ShowToolCall(toolCall)
		}
		
		m.statusBar.Clear()
		return m, nil
		
	case streamMsg:
		// Handle streaming response (if implemented)
		return m, nil
		
	case components.ErrorMsg:
		m.err = msg.Error
		m.streaming = false
		m.inputArea.SetEnabled(true)
		m.statusBar.SetError(msg.Error)
		
		if !m.ready {
			// Show error during initialization
			m.header.SetConnectionState(false)
		}
		return m, nil
		
	case components.ComponentMessage:
		// Handle component messages
		switch msg.Type {
		case components.MsgTypeInput:
			if inputMsg, ok := msg.Payload.(components.InputMsg); ok {
				if !m.streaming {
					return m, m.sendMessage(inputMsg.Text)
				}
			}
		}
	}
	
	// Update all components
	var cmd tea.Cmd
	var component components.Component

	component, cmd = m.header.Update(msg)
	m.header = component.(*components.Header)
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	component, cmd = m.messageList.Update(msg)
	m.messageList = component.(*components.MessageList)
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	component, cmd = m.statusBar.Update(msg)
	m.statusBar = component.(*components.StatusBar)
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	if m.showToolDisplay {
		component, cmd = m.toolDisplay.Update(msg)
		m.toolDisplay = component.(*components.ToolDisplay)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
	}
	
	return m, tea.Batch(cmds...)
}

// View renders the complete chat interface
func (m *ChatModel) View() string {
	if m.width == 0 || m.height == 0 {
		return ""
	}
	
	if !m.ready {
		// Show initialization screen
		if m.err != nil {
			return styles.ErrorStyle().Render(fmt.Sprintf("❌ Initialization Error: %v\n\nPlease authenticate first using: arien-ai auth login", m.err))
		}
		return styles.StatusStyle().Render("🚀 Initializing Arien AI CLI...")
	}
	
	var b strings.Builder
	
	// Header
	b.WriteString(m.header.View())
	b.WriteString("\n")
	
	// Main content area
	if m.showToolDisplay && m.toolDisplay.GetToolCount() > 0 {
		// Split layout: messages on left, tools on right
		messageWidth := m.width - m.toolDisplayWidth - 1 // -1 for separator
		toolWidth := m.toolDisplayWidth
		
		messageHeight := m.height - m.headerHeight - m.inputHeight - m.statusHeight - 1
		
		// Set component sizes
		m.messageList.SetSize(messageWidth, messageHeight)
		m.toolDisplay.SetSize(toolWidth, messageHeight)
		
		// Render side by side
		messageLines := strings.Split(m.messageList.View(), "\n")
		toolLines := strings.Split(m.toolDisplay.View(), "\n")
		
		maxLines := len(messageLines)
		if len(toolLines) > maxLines {
			maxLines = len(toolLines)
		}
		
		for i := 0; i < maxLines; i++ {
			var messageLine, toolLine string
			
			if i < len(messageLines) {
				messageLine = messageLines[i]
			}
			if i < len(toolLines) {
				toolLine = toolLines[i]
			}
			
			// Pad message line to full width
			if len(messageLine) < messageWidth {
				messageLine += strings.Repeat(" ", messageWidth-len(messageLine))
			}
			
			b.WriteString(messageLine)
			b.WriteString("│") // Separator
			b.WriteString(toolLine)
			b.WriteString("\n")
		}
	} else {
		// Full width for messages
		messageHeight := m.height - m.headerHeight - m.inputHeight - m.statusHeight - 1
		m.messageList.SetSize(m.width, messageHeight)
		b.WriteString(m.messageList.View())
		b.WriteString("\n")
	}
	
	// Input area
	m.inputArea.SetSize(m.width, m.inputHeight)
	b.WriteString(m.inputArea.View())
	b.WriteString("\n")
	
	// Status bar
	m.statusBar.SetSize(m.width, m.statusHeight)
	b.WriteString(m.statusBar.View())
	
	return b.String()
}

// updateLayout updates component layouts based on current dimensions
func (m *ChatModel) updateLayout() {
	if m.showToolDisplay {
		m.toolDisplayWidth = m.width * 40 / 100 // 40% of width
		if m.toolDisplayWidth < 20 {
			m.toolDisplayWidth = 20
		}
		if m.toolDisplayWidth > m.width-20 {
			m.toolDisplayWidth = m.width - 20
		}
	}
	
	// Update component sizes
	m.header.SetSize(m.width, m.headerHeight)
	m.statusBar.SetSize(m.width, m.statusHeight)
	m.inputArea.SetSize(m.width, m.inputHeight)
}

// sendMessage sends a user message to the LLM
func (m *ChatModel) sendMessage(userInput string) tea.Cmd {
	m.streaming = true
	m.inputArea.SetEnabled(false)
	m.statusBar.SetLoadingWithText("Sending message...")

	// Add user message to display
	userMessage := components.ChatMessage{
		Role:      "user",
		Content:   userInput,
		Timestamp: time.Now(),
	}
	m.messageList.AddMessage(userMessage)

	return func() tea.Msg {
		ctx := context.Background()

		// Get all messages for context
		messages := m.getAllMessages()

		// Convert to LLM format
		var llmMessages []llm.Message
		for _, msg := range messages {
			llmMessages = append(llmMessages, llm.Message{
				Role:      msg.Role,
				Content:   msg.Content,
				ToolCalls: msg.ToolCalls,
			})
		}

		// Get available tools
		toolDefinitions := m.toolManager.GetToolDefinitions()
		var llmTools []llm.Tool
		for _, toolDef := range toolDefinitions {
			llmTools = append(llmTools, llm.Tool{
				Type:     toolDef.Type,
				Function: llm.Function{
					Name:        toolDef.Function.Name,
					Description: toolDef.Function.Description,
					Parameters:  toolDef.Function.Parameters,
				},
			})
		}

		// Create request
		request := llm.ChatRequest{
			Model:    m.model,
			Messages: llmMessages,
			Tools:    llmTools,
		}

		// Send request
		resp, err := m.llmManager.Chat(ctx, m.provider, request)
		if err != nil {
			logger.Error("Chat error:", err)
			return components.ErrorMsg{Error: err}
		}

		if len(resp.Choices) > 0 {
			choice := resp.Choices[0]

			// Handle tool calls if present
			if len(choice.Message.ToolCalls) > 0 {
				return m.handleToolCalls(ctx, choice.Message.ToolCalls, choice.Message.Content)
			}

			return responseMsg{
				content:   choice.Message.Content,
				toolCalls: choice.Message.ToolCalls,
			}
		}

		return components.ErrorMsg{Error: fmt.Errorf("no response from LLM")}
	}
}

// getAllMessages gets all messages from the message list
func (m *ChatModel) getAllMessages() []components.ChatMessage {
	// This is a simplified version - in a real implementation,
	// you'd need to extract messages from the message list component
	// For now, we'll maintain a separate message store or modify the component interface
	return []components.ChatMessage{}
}

// handleToolCalls executes tool calls and continues the conversation
func (m *ChatModel) handleToolCalls(ctx context.Context, toolCalls []llm.ToolCall, assistantContent string) tea.Msg {

	// Execute tool calls
	for _, toolCall := range toolCalls {
		m.toolDisplay.ShowToolCall(toolCall)

		result, err := m.toolManager.ExecuteTool(ctx, toolCall.Function.Name, toolCall.Function.Arguments)
		if err != nil {
			m.toolDisplay.ShowToolResult(toolCall, fmt.Sprintf("Error: %v", err), false)
			continue
		}

		m.toolDisplay.ShowToolResult(toolCall, result.Message, result.Success)
	}

	// For now, return the assistant message
	// In a full implementation, you'd continue the conversation with tool results
	return responseMsg{
		content:   assistantContent,
		toolCalls: toolCalls,
	}
}

// Message types
type readyMsg struct{}
type responseMsg struct {
	content   string
	toolCalls []llm.ToolCall
}
type streamMsg struct {
	content string
}
