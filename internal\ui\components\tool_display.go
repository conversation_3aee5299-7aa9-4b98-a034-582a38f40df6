/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"arien-ai-cli/internal/ui/styles"
	"arien-ai-cli/internal/llm"
)

// ToolCall represents a tool call with result
type ToolCallResult struct {
	ToolCall  llm.ToolCall
	Result    string
	Success   bool
	Timestamp time.Time
	Duration  time.Duration
}

// ToolDisplay represents a tool display component
type ToolDisplay struct {
	BaseComponent
	toolCalls    []ToolCallResult
	maxCalls     int
	showDetails  bool
	autoScroll   bool
	scrollOffset int
}

// NewToolDisplay creates a new tool display component
func NewToolDisplay() *ToolDisplay {
	return &ToolDisplay{
		BaseComponent: BaseComponent{},
		toolCalls:     []ToolCallResult{},
		maxCalls:      50, // Limit to prevent memory issues
		showDetails:   true,
		autoScroll:    true,
		scrollOffset:  0,
	}
}

// Update handles messages and updates the tool display
func (td *ToolDisplay) Update(msg tea.Msg) (Component, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		td.SetSize(msg.Width, msg.Height)
		return td, nil
		
	case tea.KeyMsg:
		switch msg.String() {
		case "up", "k":
			if td.scrollOffset > 0 {
				td.scrollOffset--
				td.autoScroll = false
			}
			return td, nil
			
		case "down", "j":
			maxScroll := td.getMaxScroll()
			if td.scrollOffset < maxScroll {
				td.scrollOffset++
			}
			return td, nil
			
		case "home":
			td.scrollOffset = 0
			td.autoScroll = false
			return td, nil
			
		case "end":
			td.scrollToBottom()
			td.autoScroll = true
			return td, nil
			
		case "d":
			td.showDetails = !td.showDetails
			return td, nil
		}
		
	case ComponentMessage:
		switch msg.Type {
		case MsgTypeTool:
			if toolMsg, ok := msg.Payload.(ToolMsg); ok {
				if toolMsg.Result != "" {
					td.ShowToolResult(toolMsg.ToolCall, toolMsg.Result, toolMsg.Success)
				} else {
					td.ShowToolCall(toolMsg.ToolCall)
				}
			}
			return td, nil
		}
	}
	
	return td, nil
}

// View renders the tool display
func (td *ToolDisplay) View() string {
	if td.width == 0 || td.height == 0 || len(td.toolCalls) == 0 {
		return ""
	}
	
	lines := td.renderToolCalls()
	
	// Calculate visible lines based on scroll offset
	startLine := td.scrollOffset
	endLine := startLine + td.height
	
	if startLine >= len(lines) {
		startLine = len(lines) - td.height
		if startLine < 0 {
			startLine = 0
		}
	}
	
	if endLine > len(lines) {
		endLine = len(lines)
	}
	
	var visibleLines []string
	if startLine < endLine {
		visibleLines = lines[startLine:endLine]
	}
	
	// Pad with empty lines if needed
	for len(visibleLines) < td.height {
		visibleLines = append(visibleLines, "")
	}
	
	return strings.Join(visibleLines, "\n")
}

// ShowToolCall displays a tool call
func (td *ToolDisplay) ShowToolCall(toolCall llm.ToolCall) {
	result := ToolCallResult{
		ToolCall:  toolCall,
		Result:    "",
		Success:   false,
		Timestamp: time.Now(),
		Duration:  0,
	}
	
	td.addToolCall(result)
}

// ShowToolResult displays a tool call with its result
func (td *ToolDisplay) ShowToolResult(toolCall llm.ToolCall, result string, success bool) {
	// Find existing tool call or create new one
	var existingIndex = -1
	for i, tc := range td.toolCalls {
		if tc.ToolCall.ID == toolCall.ID {
			existingIndex = i
			break
		}
	}
	
	if existingIndex >= 0 {
		// Update existing tool call
		td.toolCalls[existingIndex].Result = result
		td.toolCalls[existingIndex].Success = success
		td.toolCalls[existingIndex].Duration = time.Since(td.toolCalls[existingIndex].Timestamp)
	} else {
		// Create new tool call result
		result := ToolCallResult{
			ToolCall:  toolCall,
			Result:    result,
			Success:   success,
			Timestamp: time.Now(),
			Duration:  0,
		}
		td.addToolCall(result)
	}
	
	if td.autoScroll {
		td.scrollToBottom()
	}
}

// ClearTools clears all tool calls
func (td *ToolDisplay) ClearTools() {
	td.toolCalls = []ToolCallResult{}
	td.scrollOffset = 0
}

// GetToolCount returns the number of tool calls
func (td *ToolDisplay) GetToolCount() int {
	return len(td.toolCalls)
}

// addToolCall adds a tool call to the list
func (td *ToolDisplay) addToolCall(result ToolCallResult) {
	td.toolCalls = append(td.toolCalls, result)
	
	// Limit tool call count to prevent memory issues
	if len(td.toolCalls) > td.maxCalls {
		td.toolCalls = td.toolCalls[len(td.toolCalls)-td.maxCalls:]
	}
	
	if td.autoScroll {
		td.scrollToBottom()
	}
}

// renderToolCalls renders all tool calls into lines
func (td *ToolDisplay) renderToolCalls() []string {
	var lines []string
	
	for i, tc := range td.toolCalls {
		toolLines := td.renderToolCall(tc, i)
		lines = append(lines, toolLines...)
		
		// Add separator between tool calls
		if i < len(td.toolCalls)-1 {
			separator := styles.SeparatorStyle().Render(strings.Repeat("─", td.width))
			lines = append(lines, separator)
		}
	}
	
	return lines
}

// renderToolCall renders a single tool call
func (td *ToolDisplay) renderToolCall(tc ToolCallResult, index int) []string {
	var lines []string
	
	// Tool call header
	timestamp := tc.Timestamp.Format("15:04:05")
	header := fmt.Sprintf("🔧 [%d] %s - %s", index+1, timestamp, tc.ToolCall.Function.Name)
	
	if tc.Duration > 0 {
		header += fmt.Sprintf(" (%v)", tc.Duration.Round(time.Millisecond))
	}
	
	styledHeader := styles.ToolCallStyle().Render(header)
	lines = append(lines, styledHeader)
	
	// Tool arguments (if showing details)
	if td.showDetails && tc.ToolCall.Function.Arguments != "" {
		argsLine := fmt.Sprintf("   Args: %s", tc.ToolCall.Function.Arguments)
		wrappedArgs := td.wrapText(argsLine, td.width-2)
		for _, line := range wrappedArgs {
			styledLine := styles.MutedStyle().Render(line)
			lines = append(lines, styledLine)
		}
	}
	
	// Tool result
	if tc.Result != "" {
		var resultStyle func() lipgloss.Style
		var icon string
		
		if tc.Success {
			resultStyle = styles.ToolResultStyle
			icon = "✅"
		} else {
			resultStyle = styles.ToolErrorStyle
			icon = "❌"
		}
		
		resultLine := fmt.Sprintf("   %s Result: %s", icon, tc.Result)
		wrappedResult := td.wrapText(resultLine, td.width-2)
		for _, line := range wrappedResult {
			styledLine := resultStyle().Render(line)
			lines = append(lines, styledLine)
		}
	} else {
		// Still executing
		pendingLine := "   ⏳ Executing..."
		styledLine := styles.LoadingStyle().Render(pendingLine)
		lines = append(lines, styledLine)
	}
	
	return lines
}

// wrapText wraps text to fit within the specified width
func (td *ToolDisplay) wrapText(text string, width int) []string {
	if width <= 0 {
		return []string{text}
	}
	
	words := strings.Fields(text)
	if len(words) == 0 {
		return []string{""}
	}
	
	var lines []string
	var currentLine strings.Builder
	
	for _, word := range words {
		// Check if adding this word would exceed the width
		if currentLine.Len() > 0 && currentLine.Len()+len(word)+1 > width {
			lines = append(lines, currentLine.String())
			currentLine.Reset()
		}
		
		if currentLine.Len() > 0 {
			currentLine.WriteString(" ")
		}
		currentLine.WriteString(word)
	}
	
	if currentLine.Len() > 0 {
		lines = append(lines, currentLine.String())
	}
	
	return lines
}

// scrollToBottom scrolls to the bottom of the tool display
func (td *ToolDisplay) scrollToBottom() {
	td.scrollOffset = td.getMaxScroll()
}

// getMaxScroll calculates the maximum scroll offset
func (td *ToolDisplay) getMaxScroll() int {
	lines := td.renderToolCalls()
	maxScroll := len(lines) - td.height
	if maxScroll < 0 {
		maxScroll = 0
	}
	return maxScroll
}

// SetShowDetails enables or disables detailed view
func (td *ToolDisplay) SetShowDetails(show bool) {
	td.showDetails = show
}

// IsShowDetails returns whether detailed view is enabled
func (td *ToolDisplay) IsShowDetails() bool {
	return td.showDetails
}

// SetAutoScroll enables or disables auto-scrolling
func (td *ToolDisplay) SetAutoScroll(auto bool) {
	td.autoScroll = auto
}

// IsAutoScroll returns whether auto-scrolling is enabled
func (td *ToolDisplay) IsAutoScroll() bool {
	return td.autoScroll
}

// SetMaxCalls sets the maximum number of tool calls to keep
func (td *ToolDisplay) SetMaxCalls(max int) {
	td.maxCalls = max
	
	// Trim tool calls if needed
	if len(td.toolCalls) > max {
		td.toolCalls = td.toolCalls[len(td.toolCalls)-max:]
	}
}
