/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"time"

	"github.com/charmbracelet/bubbletea"
	"arien-ai-cli/internal/llm"
)

// Component represents a reusable UI component
type Component interface {
	// Update handles messages and updates the component
	Update(msg tea.Msg) (Component, tea.Cmd)
	
	// View renders the component
	View() string
	
	// SetSize sets the component's dimensions
	SetSize(width, height int)
	
	// GetSize returns the component's current dimensions
	GetSize() (width, height int)
}

// BaseComponent provides common functionality for all components
type BaseComponent struct {
	width  int
	height int
}

// SetSize sets the component's dimensions
func (c *BaseComponent) SetSize(width, height int) {
	c.width = width
	c.height = height
}

// GetSize returns the component's current dimensions
func (c *BaseComponent) GetSize() (width, height int) {
	return c.width, c.height
}

// ChatMessage represents a chat message with metadata
type ChatMessage struct {
	Role      string
	Content   string
	Timestamp time.Time
	ToolCalls []llm.ToolCall
}

// ComponentState represents the state of a component
type ComponentState int

const (
	StateNormal ComponentState = iota
	StateLoading
	StateError
	StateDisabled
)

// HeaderComponent interface for header components
type HeaderComponent interface {
	Component
	SetProvider(provider, model string)
	SetStatus(status string)
	SetConnectionState(connected bool)
}

// MessageListComponent interface for message list components
type MessageListComponent interface {
	Component
	AddMessage(message ChatMessage)
	SetMessages(messages []ChatMessage)
	ScrollToBottom()
	GetMessageCount() int
	Clear()
}

// InputComponent interface for input components
type InputComponent interface {
	Component
	GetInput() string
	SetInput(text string)
	ClearInput()
	SetEnabled(enabled bool)
	IsEnabled() bool
	SetPlaceholder(text string)
}

// StatusBarComponent interface for status bar components
type StatusBarComponent interface {
	Component
	SetStatus(status string)
	SetError(err error)
	ClearError()
	SetLoading(loading bool)
	IsLoading() bool
}

// ToolDisplayComponent interface for tool display components
type ToolDisplayComponent interface {
	Component
	ShowToolCall(toolCall llm.ToolCall)
	ShowToolResult(toolCall llm.ToolCall, result string, success bool)
	ClearTools()
	GetToolCount() int
}

// ComponentMessage represents messages between components
type ComponentMessage struct {
	Type    string
	Payload interface{}
}

// Common component message types
const (
	MsgTypeResize      = "resize"
	MsgTypeThemeChange = "theme_change"
	MsgTypeError       = "error"
	MsgTypeStatus      = "status"
	MsgTypeInput       = "input"
	MsgTypeMessage     = "message"
	MsgTypeTool        = "tool"
)

// ResizeMsg represents a resize message
type ResizeMsg struct {
	Width  int
	Height int
}

// ThemeChangeMsg represents a theme change message
type ThemeChangeMsg struct {
	ThemeName string
}

// ErrorMsg represents an error message
type ErrorMsg struct {
	Error error
}

// StatusMsg represents a status message
type StatusMsg struct {
	Status string
}

// InputMsg represents an input message
type InputMsg struct {
	Text string
}

// MessageMsg represents a chat message
type MessageMsg struct {
	Message ChatMessage
}

// ToolMsg represents a tool-related message
type ToolMsg struct {
	ToolCall llm.ToolCall
	Result   string
	Success  bool
}
