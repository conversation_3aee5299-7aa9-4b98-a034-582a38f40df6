/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package ui

import (
	"github.com/charmbracelet/lipgloss"
	"arien-ai-cli/internal/ui/styles"
)

// Compatibility wrappers for the old theme interface

// Theme represents a UI theme - compatibility wrapper
type Theme = styles.Theme

// GetTheme returns the current theme
func GetTheme() Theme {
	return styles.GetTheme()
}

// SetTheme sets the current theme by name
func SetTheme(name string) bool {
	return styles.SetTheme(name)
}

// GetAvailableThemes returns all available themes
func GetAvailableThemes() map[string]Theme {
	return styles.GetAvailableThemes()
}

// GetThemeNames returns all theme names
func GetThemeNames() []string {
	return styles.GetThemeNames()
}

// InitializeThemes sets the default theme
func InitializeThemes() {
	styles.Initialize()
}

// Style function wrappers for compatibility
func PrimaryStyle() lipgloss.Style {
	return styles.PrimaryStyle()
}

func SecondaryStyle() lipgloss.Style {
	return styles.SecondaryStyle()
}

func AccentStyle() lipgloss.Style {
	return styles.AccentStyle()
}

func SuccessStyle() lipgloss.Style {
	return styles.SuccessStyle()
}

func WarningStyle() lipgloss.Style {
	return styles.WarningStyle()
}

func ErrorStyle() lipgloss.Style {
	return styles.ErrorStyle()
}

func MutedStyle() lipgloss.Style {
	return styles.MutedStyle()
}

func HeaderStyle() lipgloss.Style {
	return styles.HeaderStyle()
}

func MessageStyle() lipgloss.Style {
	return styles.MessageStyle()
}

func UserMessageStyle() lipgloss.Style {
	return styles.UserMessageStyle()
}

func AssistantMessageStyle() lipgloss.Style {
	return styles.AssistantMessageStyle()
}

func InputStyle() lipgloss.Style {
	return styles.InputStyle()
}

func StatusStyle() lipgloss.Style {
	return styles.StatusStyle()
}
