/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"strings"

	"github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"arien-ai-cli/internal/ui/styles"
)

// InputArea represents an input area component with cursor support
type InputArea struct {
	BaseComponent
	input       string
	cursor      int
	placeholder string
	enabled     bool
	focused     bool
	multiline   bool
}

// NewInputArea creates a new input area component
func NewInputArea() *InputArea {
	return &InputArea{
		BaseComponent: BaseComponent{},
		input:         "",
		cursor:        0,
		placeholder:   "Type your message here...",
		enabled:       true,
		focused:       true,
		multiline:     false,
	}
}

// Update handles messages and updates the input area
func (ia *InputArea) Update(msg tea.Msg) (Component, tea.Cmd) {
	if !ia.enabled {
		return ia, nil
	}
	
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		ia.SetSize(msg.Width, 3) // Input area is typically 3 lines
		return ia, nil
		
	case tea.KeyMsg:
		switch msg.String() {
		case "left":
			if ia.cursor > 0 {
				ia.cursor--
			}
			return ia, nil
			
		case "right":
			if ia.cursor < len(ia.input) {
				ia.cursor++
			}
			return ia, nil
			
		case "home", "ctrl+a":
			ia.cursor = 0
			return ia, nil
			
		case "end", "ctrl+e":
			ia.cursor = len(ia.input)
			return ia, nil
			
		case "backspace":
			if ia.cursor > 0 {
				ia.input = ia.input[:ia.cursor-1] + ia.input[ia.cursor:]
				ia.cursor--
			}
			return ia, nil
			
		case "delete", "ctrl+d":
			if ia.cursor < len(ia.input) {
				ia.input = ia.input[:ia.cursor] + ia.input[ia.cursor+1:]
			}
			return ia, nil
			
		case "ctrl+u":
			// Delete from cursor to beginning of line
			ia.input = ia.input[ia.cursor:]
			ia.cursor = 0
			return ia, nil
			
		case "ctrl+k":
			// Delete from cursor to end of line
			ia.input = ia.input[:ia.cursor]
			return ia, nil
			
		case "ctrl+w":
			// Delete previous word
			if ia.cursor > 0 {
				// Find the start of the previous word
				start := ia.cursor - 1
				for start > 0 && ia.input[start] == ' ' {
					start--
				}
				for start > 0 && ia.input[start] != ' ' {
					start--
				}
				if ia.input[start] == ' ' {
					start++
				}
				
				ia.input = ia.input[:start] + ia.input[ia.cursor:]
				ia.cursor = start
			}
			return ia, nil
			
		case "enter":
			if !ia.multiline {
				// Single line mode - return input message
				return ia, func() tea.Msg {
					return ComponentMessage{
						Type:    MsgTypeInput,
						Payload: InputMsg{Text: ia.input},
					}
				}
			} else {
				// Multiline mode - add newline
				ia.input = ia.input[:ia.cursor] + "\n" + ia.input[ia.cursor:]
				ia.cursor++
			}
			return ia, nil
			
		default:
			// Handle regular character input
			if len(msg.String()) == 1 {
				char := msg.String()
				ia.input = ia.input[:ia.cursor] + char + ia.input[ia.cursor:]
				ia.cursor++
			}
			return ia, nil
		}
		
	case ComponentMessage:
		switch msg.Type {
		case "focus":
			ia.focused = true
			return ia, nil
		case "blur":
			ia.focused = false
			return ia, nil
		}
	}
	
	return ia, nil
}

// View renders the input area
func (ia *InputArea) View() string {
	if ia.width == 0 {
		return ""
	}
	
	var b strings.Builder
	
	// Input prompt
	prompt := " > : "
	b.WriteString(styles.PrimaryStyle().Render(prompt))
	
	// Input text with cursor
	inputText := ia.input
	displayText := inputText
	
	if ia.enabled {
		// Show cursor
		if ia.cursor <= len(inputText) {
			if ia.cursor == len(inputText) {
				displayText = inputText + "│"
			} else {
				displayText = inputText[:ia.cursor] + "│" + inputText[ia.cursor:]
			}
		}
	} else {
		// Show disabled state
		displayText = styles.MutedStyle().Render("(Input disabled)")
	}
	
	// Show placeholder if input is empty
	if len(ia.input) == 0 && ia.enabled {
		displayText = styles.MutedStyle().Render(ia.placeholder)
	}
	
	// Apply input styling
	var inputStyle func() lipgloss.Style
	if ia.focused && ia.enabled {
		inputStyle = styles.InputFocusedStyle
	} else {
		inputStyle = styles.InputStyle
	}
	
	// Calculate available width for input (minus prompt)
	availableWidth := ia.width - len(prompt) - 4 // Account for padding and borders
	if availableWidth < 10 {
		availableWidth = 10
	}
	
	styledInput := inputStyle().
		Width(availableWidth).
		Render(displayText)
	
	b.WriteString(styledInput)
	
	// Add help text on second line if there's space
	if ia.height > 1 {
		b.WriteString("\n")
		helpText := ""
		if ia.enabled {
			if ia.multiline {
				helpText = "Enter: new line, Ctrl+Enter: send"
			} else {
				helpText = "Enter: send, Ctrl+C: exit"
			}
		}
		
		if helpText != "" {
			styledHelp := styles.MutedStyle().
				Width(ia.width).
				Align(lipgloss.Center).
				Render(helpText)
			b.WriteString(styledHelp)
		}
	}
	
	return b.String()
}

// GetInput returns the current input text
func (ia *InputArea) GetInput() string {
	return ia.input
}

// SetInput sets the input text
func (ia *InputArea) SetInput(text string) {
	ia.input = text
	ia.cursor = len(text)
}

// ClearInput clears the input text
func (ia *InputArea) ClearInput() {
	ia.input = ""
	ia.cursor = 0
}

// SetEnabled enables or disables the input area
func (ia *InputArea) SetEnabled(enabled bool) {
	ia.enabled = enabled
	if !enabled {
		ia.focused = false
	}
}

// IsEnabled returns whether the input area is enabled
func (ia *InputArea) IsEnabled() bool {
	return ia.enabled
}

// SetPlaceholder sets the placeholder text
func (ia *InputArea) SetPlaceholder(text string) {
	ia.placeholder = text
}

// GetPlaceholder returns the placeholder text
func (ia *InputArea) GetPlaceholder() string {
	return ia.placeholder
}

// SetFocused sets the focus state
func (ia *InputArea) SetFocused(focused bool) {
	ia.focused = focused
}

// IsFocused returns whether the input area is focused
func (ia *InputArea) IsFocused() bool {
	return ia.focused
}

// SetMultiline enables or disables multiline mode
func (ia *InputArea) SetMultiline(multiline bool) {
	ia.multiline = multiline
}

// IsMultiline returns whether multiline mode is enabled
func (ia *InputArea) IsMultiline() bool {
	return ia.multiline
}

// GetCursorPosition returns the current cursor position
func (ia *InputArea) GetCursorPosition() int {
	return ia.cursor
}

// SetCursorPosition sets the cursor position
func (ia *InputArea) SetCursorPosition(pos int) {
	if pos < 0 {
		pos = 0
	}
	if pos > len(ia.input) {
		pos = len(ia.input)
	}
	ia.cursor = pos
}

// GetHeight returns the input area height
func (ia *InputArea) GetHeight() int {
	return 3 // Fixed height for now
}
